import { useState, useEffect } from "react";


function useDebounce<T>(value: T, delay: number = 300): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const debounceTimer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay)

        return () => clearTimeout(debounceTimer);
    }, [value, delay])

    return debouncedValue;
}


export default useDebounce;