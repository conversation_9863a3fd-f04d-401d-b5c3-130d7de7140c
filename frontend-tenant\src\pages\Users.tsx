import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users as UsersIcon,
  Calendar,
  UserCheck,
  UserX,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link } from "react-router-dom";
import UserAPI from "@/api/UserAPI";
import {
  UserPageVo,
  UserQueryParam,
  UserPageResponse,
  CreateUserRequest,
} from "@/types/user";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import useDebounce from "../hooks/useDebounce";
import { useToast } from "@/hooks/use-toast";
import ObjectUtil from "@/utils/objectUtil";

const genderMap = { 0: "保密", 1: "男", 2: "女" };
const statusMap = { Active: "正常", Inactive: "禁用" };
enum UserStatusEnum {
  Active,
  Inactive,
}

export default function Users() {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const searchDebouncedValue = useDebounce(searchTerm, 500);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [userPage, setUserPage] = useState<UserPageVo[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(10);

  const [newUser, setNewUser] = useState<CreateUserRequest>({
    uniqueUserKey: "",
    displayName: "",
    password: "",
    gender: 0,
  });
  const [creating, setCreating] = useState(false);

  // 获取用户列表数据
  const fetchUsers = useCallback(
    async (params?: Partial<UserQueryParam>) => {
      try {
        setLoading(true);
        const requestParams: UserQueryParam = {
          pageNum: params?.pageNum || pageNum,
          pageSize: params?.pageSize || pageSize,
          keywords:
            params?.keywords !== undefined
              ? params.keywords
              : searchDebouncedValue || undefined,
        };

        const response: UserPageResponse = await UserAPI.getUserPage(
          requestParams
        );
        setUserPage(response.list);
        setTotal(response.total);
      } catch (error) {
        console.error("获取用户列表失败:", error);
      } finally {
        setLoading(false);
      }
    },
    [pageNum, pageSize, searchDebouncedValue]
  );

  // 初始化加载数据
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 解析角色字符串为数组
  const parseRoles = (roles: string): string[] => {
    if (!roles) return [];
    try {
      // 如果是逗号分隔的字符串
      return roles
        .split(",")
        .map((role) => role.trim())
        .filter(Boolean);
    } catch {
      // 解析失败，返回原字符串作为单个角色
      return [roles];
    }
  };

  // 创建用户
  const handleCreateUser = async () => {
    if (!newUser.uniqueUserKey.trim()) {
      toast({
        title: "错误",
        description: "用户名不能为空",
        variant: "destructive",
      });
      return;
    }

    try {
      setCreating(true);
      await UserAPI.createUser({
        ...newUser,
        uniqueUserKey: newUser.uniqueUserKey.trim(),
        displayName: newUser.displayName.trim(),
        password: newUser.password.trim(),
      });

      toast({ title: "成功", description: "用户添加成功!" });
      setNewUser(ObjectUtil.resetToDefault(newUser));
      fetchUsers();
      setIsCreateDialogOpen(false);
    } catch (error) {
      toast({
        title: "错误",
        description: "添加用户失败!",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  // 删除用户
  const handleDeleteUser = async (id: string) => {
    if (!confirm("确认删除该用户吗?")) {
      return;
    }

    try {
      await UserAPI.deleteUserById(id);
      toast({ title: "成功", description: "删除用户成功..." });
      fetchUsers();
    } catch (error) {
      toast({ title: "错误", description: "删除用户失败..." });
    }
  };

  // 禁用/启用用户
  const handleChangeUserStatus = async (id: string, status: string) => {
    if (status === UserStatusEnum[0]) {
      status = UserStatusEnum[1];
    } else {
      status = UserStatusEnum[0];
    }
    try {
      await UserAPI.changeUserStatus(id, status);
      toast({ title: "成功", description: "操作成功..." });
      fetchUsers();
    } catch (error) {
      toast({ title: "失败", description: "操作失败..." });
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            登录账号管理
          </h1>
          <p className="text-muted-foreground mt-1">管理系统登录账户和权限</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Plus className="mr-2 h-4 w-4" />
              添加成员
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] bg-card border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground">添加新成员</DialogTitle>
              <DialogDescription>创建一个新的成员账户</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  value={newUser.uniqueUserKey}
                  onChange={(e) => {
                    setNewUser({ ...newUser, uniqueUserKey: e.target.value });
                  }}
                  placeholder="输入用户名"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="nickname">昵称</Label>
                <Input
                  id="nickname"
                  value={newUser.displayName}
                  onChange={(e) => {
                    setNewUser({ ...newUser, displayName: e.target.value });
                  }}
                  placeholder="输入显示昵称"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={newUser.password}
                  onChange={(e) =>
                    setNewUser({ ...newUser, password: e.target.value })
                  }
                  placeholder="输入初始密码"
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="gender">性别</Label>
                <Select
                  value={newUser.gender?.toString()}
                  onValueChange={(value) => {
                    setNewUser((prev) => ({
                      ...prev,
                      gender: Number(value),
                    }));
                  }}
                >
                  <SelectTrigger className="bg-muted border-border">
                    <SelectValue placeholder="选择性别" />
                  </SelectTrigger>
                  <SelectContent className="bg-popover border-border">
                    <SelectItem value="0">保密</SelectItem>
                    <SelectItem value="1">男</SelectItem>
                    <SelectItem value="2">女</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                className="bg-gradient-primary hover:opacity-90"
                onClick={handleCreateUser}
                disabled={creating}
              >
                创建成员
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总成员数
            </CardTitle>
            <UsersIcon className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{total}</div>
            <p className="text-xs text-muted-foreground">总计成员数量</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              活跃成员
            </CardTitle>
            <UserCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {userPage.filter((user) => user.status === "Active").length}
            </div>
            <p className="text-xs text-muted-foreground">正常状态</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              禁用成员
            </CardTitle>
            <UserX className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {userPage.filter((user) => user.status === "Inactive").length}
            </div>
            <p className="text-xs text-muted-foreground">暂停使用</p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              管理员
            </CardTitle>
            <UsersIcon className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">
              {
                userPage.filter((user) =>
                  parseRoles(user.roleKey).includes("管理员")
                ).length
              }
            </div>
            <p className="text-xs text-muted-foreground">管理权限</p>
          </CardContent>
        </Card>
      </div>

      {/* 用户列表 */}
      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索成员用户名或昵称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">
                  成员信息
                </TableHead>
                <TableHead className="text-muted-foreground">性别</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground">角色</TableHead>
                <TableHead className="text-muted-foreground">
                  创建时间
                </TableHead>
                <TableHead className="text-muted-foreground">
                  更新时间
                </TableHead>
                <TableHead className="text-muted-foreground text-right">
                  操作
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-muted-foreground">加载中...</p>
                  </TableCell>
                </TableRow>
              ) : !userPage || userPage.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <p className="text-muted-foreground">暂无数据</p>
                  </TableCell>
                </TableRow>
              ) : (
                userPage.map((user: UserPageVo) => (
                  <TableRow
                    key={user.id}
                    className="border-border hover:bg-muted/50"
                  >
                    <TableCell className="font-medium">
                      <Link
                        to={`/users/${user.id}`}
                        className="flex items-center space-x-3 hover:opacity-80"
                      >
                        <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                          <span className="text-xs font-semibold text-primary-foreground">
                            {user.uniqueUserKey?.charAt(0) ||
                              user.uniqueUserKey.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <div className="font-semibold text-foreground">
                            {user.uniqueUserKey}
                          </div>
                          {user.displayName && (
                            <div className="text-sm text-muted-foreground">
                              {user.displayName}
                            </div>
                          )}
                        </div>
                      </Link>
                    </TableCell>
                    <TableCell>
                      <span className="text-foreground">
                        {genderMap[user.gender as keyof typeof genderMap]}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          user.status === "Active" ? "default" : "secondary"
                        }
                        className={
                          user.status === "Active"
                            ? "bg-green-500/20 text-green-500 border-green-500/30"
                            : "bg-red-500/20 text-red-500 border-red-500/30"
                        }
                      >
                        {statusMap[user.status as keyof typeof statusMap]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {parseRoles(user.roleKey).map(
                          (role: string, index: number) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="text-xs"
                            >
                              {role}
                            </Badge>
                          )
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-muted-foreground">
                        <Calendar className="mr-1 h-3 w-3" />
                        {user.createAt}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">
                        {user.updateAt}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="bg-popover border-border"
                        >
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link
                              to={`/users/${user.id}`}
                              className="flex items-center"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              查看详情
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              to={`/users/${user.id}/edit`}
                              className="flex items-center"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              编辑成员
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className={
                              user.status === "Active"
                                ? "text-orange-500"
                                : "text-green-500"
                            }
                            onClick={() => {
                              handleChangeUserStatus(user.id, user.status);
                            }}
                          >
                            {user.status === "Active" ? (
                              <>
                                <UserX className="mr-2 h-4 w-4" />
                                禁用成员
                              </>
                            ) : (
                              <>
                                <UserCheck className="mr-2 h-4 w-4" />
                                启用成员
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteUser(user.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除成员
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* 分页 */}
          {!loading && total > 0 && (
            <div className="flex items-center justify-between mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => pageNum > 1 && setPageNum(pageNum - 1)}
                      className={
                        pageNum <= 1
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>

                  {/* 页码 */}
                  {Array.from(
                    { length: Math.ceil(total / pageSize) },
                    (_, i) => i + 1
                  )
                    .filter((page) => {
                      const totalPages = Math.ceil(total / pageSize);
                      if (totalPages <= 7) return true;
                      if (page === 1 || page === totalPages) return true;
                      if (Math.abs(page - pageNum) <= 2) return true;
                      return false;
                    })
                    .map((page, index, array) => {
                      const showEllipsis =
                        index > 0 && page - array[index - 1] > 1;
                      return (
                        <div key={page} style={{ display: "contents" }}>
                          {showEllipsis && (
                            <PaginationItem key={`ellipsis-${page}`}>
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          )}
                          <PaginationItem key={`page-${page}`}>
                            <PaginationLink
                              onClick={() => setPageNum(page)}
                              isActive={pageNum === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        </div>
                      );
                    })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        pageNum < Math.ceil(total / pageSize) &&
                        setPageNum(pageNum + 1)
                      }
                      className={
                        pageNum >= Math.ceil(total / pageSize)
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
              <div className="text-sm text-muted-foreground">
                共 {total} 条记录，{pageNum}/{pageSize} 条/页
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
