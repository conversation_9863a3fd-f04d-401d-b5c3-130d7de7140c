import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Search, MoreHorizontal, Edit, Trash2, Activity, Calendar, Zap, Loader2, AlertCircle } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import ApplicationAPI from "@/api/ApplicationAPI";
import type { ApplicationPageVO, ApplicationStatus } from "@/types/application";



export default function Applications() {
  const { toast } = useToast();
  const [applications, setApplications] = useState<ApplicationPageVO[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [creating, setCreating] = useState(false);
  const [newAppData, setNewAppData] = useState({ displayName: "", description: "" });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const mapStatus = (status: string): ApplicationStatus => {
    switch (status) {
      case 'active': return '启用';
      case 'inactive':
      case 'disabled': return '禁用';
      default: return status as ApplicationStatus;
    }
  };

  const loadApplications = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await ApplicationAPI.getApplicationPage({
        pageNum: currentPage,
        pageSize,
        searchKeyword: searchTerm || undefined
      });

      if (result && Array.isArray(result.list)) {
        const mappedApplications = result.list.map(app => ({ ...app, status: mapStatus(app.status) }));
        setApplications(mappedApplications);
        setTotal(result.total || 0);
      } else {
        setApplications([]);
        setTotal(0);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取应用列表失败';
      setError(errorMessage);
      setApplications([]);
      setTotal(0);
      toast({ title: "错误", description: errorMessage, variant: "destructive" });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApplication = async () => {
    if (!newAppData.displayName.trim()) {
      toast({ title: "错误", description: "应用名称不能为空", variant: "destructive" });
      return;
    }

    try {
      setCreating(true);
      await ApplicationAPI.createApplication({
        displayName: newAppData.displayName.trim(),
        description: newAppData.description.trim()
      });
      toast({ title: "成功", description: "应用创建成功" });
      setIsCreateDialogOpen(false);
      setNewAppData({ displayName: "", description: "" });
      loadApplications();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建应用失败';
      toast({ title: "错误", description: errorMessage, variant: "destructive" });
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteApplication = async (id: string) => {
    try {
      await ApplicationAPI.deleteApplication(id);
      toast({ title: "成功", description: "应用删除成功" });
      loadApplications();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除应用失败';
      toast({ title: "错误", description: errorMessage, variant: "destructive" });
    }
  };

  useEffect(() => {
    loadApplications();
  }, []);

  useEffect(() => {
    if (currentPage > 1) {
      loadApplications();
    }
  }, [currentPage]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        loadApplications();
      } else {
        setCurrentPage(1);
      }
    }, 500);
    return () => clearTimeout(timer);
  }, [searchTerm]);

  const totalApplications = total;
  const enabledApplications = applications?.filter(app => app.status === '启用').length || 0;
  const totalAgents = applications?.reduce((sum, app) => sum + (app.agentCount || 0), 0) || 0;
  const totalSessions = applications?.reduce((sum, app) => sum + (app.sessionCount || 0), 0) || 0;

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">应用管理</h1>
          <p className="text-muted-foreground mt-1">管理您的AI应用和服务</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-primary hover:opacity-90">
              <Plus className="mr-2 h-4 w-4" />
              创建应用
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px] bg-card border-border">
            <DialogHeader>
              <DialogTitle className="text-foreground">创建新应用</DialogTitle>
              <DialogDescription>
                创建一个新的AI应用来管理您的智能体和服务
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="displayName" className="text-foreground">应用名称</Label>
                <Input
                  id="displayName"
                  value={newAppData.displayName}
                  onChange={(e) => setNewAppData({ ...newAppData, displayName: e.target.value })}
                  placeholder="输入应用名称..."
                  className="bg-muted border-border"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description" className="text-foreground">应用描述</Label>
                <Textarea
                  id="description"
                  value={newAppData.description}
                  onChange={(e) => setNewAppData({ ...newAppData, description: e.target.value })}
                  placeholder="描述应用的用途和功能..."
                  className="bg-muted border-border"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
                disabled={creating}
              >
                取消
              </Button>
              <Button
                onClick={handleCreateApplication}
                disabled={creating || !newAppData.displayName.trim()}
                className="bg-gradient-primary hover:opacity-90"
              >
                {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {creating ? '创建中...' : '创建应用'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总应用数
            </CardTitle>
            <Zap className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{totalApplications}</div>
            <p className="text-xs text-muted-foreground">
              +2 较上月
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              启用中
            </CardTitle>
            <Activity className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{enabledApplications}</div>
            <p className="text-xs text-muted-foreground">
              正常运行
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总智能体
            </CardTitle>
            <Activity className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{totalAgents}</div>
            <p className="text-xs text-muted-foreground">
              分布在各应用
            </p>
          </CardContent>
        </Card>
        <Card className="bg-card border-border shadow-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              总会话数
            </CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-foreground">{totalSessions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              累计对话
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-card border-border shadow-card">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索应用名称或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-muted border-border"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="text-muted-foreground">应用名称</TableHead>
                <TableHead className="text-muted-foreground">状态</TableHead>
                <TableHead className="text-muted-foreground">智能体</TableHead>
                <TableHead className="text-muted-foreground">会话数</TableHead>
                <TableHead className="text-muted-foreground">创建时间</TableHead>
                <TableHead className="text-muted-foreground">最后活动</TableHead>
                <TableHead className="text-muted-foreground text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-muted-foreground">加载中...</p>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <AlertCircle className="h-6 w-6 mx-auto mb-2 text-destructive" />
                    <p className="text-destructive">{error}</p>
                  </TableCell>
                </TableRow>
              ) : !applications || applications.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <p className="text-muted-foreground">暂无应用数据</p>
                  </TableCell>
                </TableRow>
              ) : applications.map((app) => (
                <TableRow key={app.id} className="border-border hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-foreground">{app.name}</div>
                      <div className="text-sm text-muted-foreground">{app.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={app.status === "启用" ? "default" : "secondary"}
                      className={app.status === "启用" ? "bg-green-500/20 text-green-500 border-green-500/30 flex items-center gap-1" : "bg-red-500/20 text-red-500 border-red-500/30 flex items-center gap-1"}
                    >
                      {app.status === "启用" ? <Activity className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                      {app.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{app.agentCount || 0} 个</span>
                  </TableCell>
                  <TableCell>
                    <span className="text-foreground">{(app.sessionCount || 0).toLocaleString()}</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-muted-foreground">
                      <Calendar className="mr-1 h-3 w-3" />
                      {app.createdAt}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-muted-foreground">{app.lastActivity}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <Link to={`/applications/${app.id}/edit`}>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            编辑
                          </DropdownMenuItem>
                        </Link>
                        <Link to={`/applications/${app.id}`}>
                          <DropdownMenuItem>
                            <Activity className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                        </Link>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => {
                            if (confirm('确定要删除这个应用吗？此操作不可恢复。')) {
                              handleDeleteApplication(app.id);
                            }
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}