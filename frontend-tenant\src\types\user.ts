export interface User {
  id: string;
  username: string;
  nickname?: string;
  gender: number;
  status: string;
  createTime: string;
  updateTime: string;
  roleNames: string;
}

export interface UserPageRequest {
  pageNum: number;
  pageSize: number;
  keywords?: string;
}

export interface UserPageResponse {
  list: User[];
  total: number;
}


export interface CreateUserRequest {
  username: string;
  nickname: string;
  password: string;
  gender: number;
}

export interface UpdateUserRequest {
  id: string,
  username: string,
  nickname: string,
  gender: number,
  status: string,
  email: string,
  phone: string,
  dept: string,
  position: string,
  description: string,
  roles: Array<string>,
}


export interface UserDetailResponse {
  id: string,
  username: string,
  nickname: string,
  gender: number,
  status: string,
  email: string,
  phone: string,
  dept: string,
  position: string,
  description: string,
  createTime: string,
  updateTime: string,
  roleNames: string,
}