/**
 * 应用相关类型定义
 */

// 应用状态枚举
export type ApplicationStatus = '启用' | '禁用';

// 分页查询参数
export interface ApplicationPageQuery {
  pageNum: number;
  pageSize: number;
  searchKeyword?: string;
}

// 应用分页列表项
export interface ApplicationPageVO {
  id: string;
  key: string;
  name: string;
  description: string;
  status: ApplicationStatus;
  icon?: string;
  agentCount: number;
  sessionCount: number;
  createdAt: string;
  lastActivity: string;
}

// 应用详情
export interface ApplicationDetailVO {
  id: string;
  key: string;
  displayName: string;
  description: string;
  status?: ApplicationStatus;
  icon?: string;
  signOutUrl?: string;
  theme?: string;
  aiAvatar?: string;
  userAvatar?: string;
  recommendedStartPrompts?: string[];
  agentOptionId?: string;
  tenantId?: string;
  createdAt?: string;
  enableUserNextPromptPrediction?: boolean;
  
  // 统计字段
  agentCount?: number;
  sessionCount?: number;
  lastActivity?: string;
  
  // 模拟数据字段
  monthlyRequests?: number;
  successRate?: number;
  avgResponseTime?: string;
  totalUsers?: number;
  usage?: {
    current: number;
    limit: number;
    percentage: number;
  };
  
  // 配置字段
  settings?: {
    autoReply: boolean;
    maxConcurrentSessions: number;
    sessionTimeout: number;
    enableLogging: boolean;
    enableAnalytics: boolean;
    rateLimitEnabled: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
  };
  
  // 安全配置
  security?: {
    requireAuth: boolean;
    enableCors: boolean;
    allowedOrigins: string[];
    ipWhitelist?: string;
  };
  
  // UI配置
  ui?: {
    icon: string;
    signOutUrl: string;
    theme: string;
    aiAvatar: string;
    userAvatar: string;
  };
  
  // 智能体列表
  agents?: {
    id: string;
    name: string;
    status: string;
    requests: number;
  }[];
  
  // 最近会话
  recentSessions?: {
    id: string;
    user: string;
    agent: string;
    messages: number;
    status: string;
    time: string;
  }[];
}

// 创建/更新应用请求
export interface ApplicationRequest {
  key?: string;
  displayName: string;
  description?: string;
  icon?: string;
  signOutUrl?: string;
  theme?: string;
  aiAvatar?: string;
  userAvatar?: string;
  recommendedStartPrompts?: string[];
  agentOptionId?: string;
  enableUserNextPromptPrediction?: boolean;
}

// 分页结果
export interface PageResult<T> {
  list: T[];
  total: number;
  size?: number;
  current?: number;
  pages?: number;
}

// API响应结果
export interface ApiResult<T = any> {
  code: number;
  data: T;
  msg: string;
  success: boolean;
}

// 应用Key检查结果
export interface ApplicationKeyCheckResult {
  exists: boolean;
}