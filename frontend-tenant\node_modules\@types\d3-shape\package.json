{"name": "@types/d3-shape", "version": "3.1.6", "description": "TypeScript definitions for d3-shape", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-shape", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "githubUsername": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "Fil", "githubUsername": "Fil", "url": "https://github.com/Fil"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-shape"}, "scripts": {}, "dependencies": {"@types/d3-path": "*"}, "typesPublisherContentHash": "4aad0e2be93cb4cd6d504f63aaf9eac0fc2d55c130084c39371f93dc8fa80520", "typeScriptVersion": "4.5"}