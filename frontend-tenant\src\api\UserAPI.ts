import request from '../utils/request';
import { User,UserPageRequest,UserPageResponse } from "../types";

const USER_BASE_URL = '/api/v1/users';



const UserAPI = {
  /**
   * 分页查询用户列表
   */
  async getUserPage(params: UserPageRequest): Promise<UserPageResponse> {
    return request<UserPageResponse>({
      url: `${USER_BASE_URL}/page`,
      method: 'get',
      params,
    });
  },

  /**
   * 根据ID获取用户详情
   */
  async getUserById(id: string): Promise<User> {
    return request<User>({
      url: `${USER_BASE_URL}/${id}`,
      method: 'get',
    });
  },

  /**
   * 创建用户
   */
  async createUser(data: Partial<User>): Promise<User> {
    return request<User>({
      url: USER_BASE_URL,
      method: 'post',
      data,
    });
  },
};

export default UserAPI;
