import request from '@/utils/request';
import type {
  ApplicationPageQuery,
  ApplicationPageVO,
  ApplicationDetailVO,
  ApplicationRequest,
  PageResult,
  ApplicationKeyCheckResult
} from '@/types/application';

const APPLICATION_API = '/api/v1/applications';

/**
 * 应用管理 API 服务类
 * 注意：request 工具已经处理了响应拦截，成功时直接返回 data 字段
 */
class ApplicationAPI {

  /**
   * 获取应用分页列表
   */
  static async getApplicationPage(params: ApplicationPageQuery): Promise<PageResult<ApplicationPageVO>> {
    return request({
      url: APPLICATION_API + '/page',
      method: 'GET',
      params
    });
  }

  /**
   * 获取应用详情
   */
  static async getApplicationDetail(id: string): Promise<ApplicationDetailVO> {
    return request({
      url: APPLICATION_API + `/${id}`,
      method: 'GET'
    });
  }

  /**
   * 创建应用
   */
  static async createApplication(data: ApplicationRequest): Promise<string> {
    return request({
      url: APPLICATION_API,
      method: 'POST',
      data
    });
  }

  /**
   * 更新应用
   */
  static async updateApplication(id: string, data: ApplicationRequest): Promise<void> {
    return request({
      url: APPLICATION_API + `/${id}`,
      method: 'PUT',
      data
    });
  }

  /**
   * 删除应用
   */
  static async deleteApplication(id: string): Promise<void> {
    return request({
      url: APPLICATION_API + `/${id}`,
      method: 'DELETE'
    });
  }

  /**
   * 批量删除应用
   */
  static async batchDeleteApplications(ids: string[]): Promise<void> {
    return request({
      url: APPLICATION_API + '/batch',
      method: 'DELETE',
      data: { ids }
    });
  }

  /**
   * 检查应用Key是否存在
   */
  static async checkApplicationKey(key: string): Promise<ApplicationKeyCheckResult> {
    return request({
      url: APPLICATION_API + '/check-key',
      method: 'GET',
      params: { key }
    });
  }
}

export default ApplicationAPI;