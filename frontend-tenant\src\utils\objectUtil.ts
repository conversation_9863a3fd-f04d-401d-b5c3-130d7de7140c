
export class ObjectUtil {


    static resetToDefault<T>(obj: T): T {
        const result = { ...obj as Record<string, unknown> } as Record<string, unknown>;

        Object.keys(result).forEach(key => {
            const value = result[key];
            const type = typeof value;

            switch (type) {
                case 'string':
                    result[key] = '';
                    break;
                case 'number':
                    result[key] = 0;
                    break;
                case 'boolean':
                    result[key] = false;
                    break;
                case 'object':
                    if (Array.isArray(value)) {
                        result[key] = [];
                    } else {
                        result[key] = null;
                    }
                    break;
                default:
                    result[key] = undefined;
            }
        });

        return result as T;
    }

}

export default ObjectUtil;